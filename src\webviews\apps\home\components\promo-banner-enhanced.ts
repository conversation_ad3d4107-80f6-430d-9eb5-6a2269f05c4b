import { consume } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { until } from 'lit/directives/until.js';
import type { Promo } from '../../../../plus/gk/models/promo';
import type { State } from '../../../home/<USER>';
import { CollapseSectionCommand } from '../../../home/<USER>';
import type { PromosContext } from '../../shared/contexts/promos';
import { promosContext } from '../../shared/contexts/promos';
import { ipcContext } from '../../shared/contexts/ipc';
import type { HostIpc } from '../../shared/ipc';
import { stateContext } from '../context';
import '../../shared/components/banner/banner';
import type { BannerDisplay } from '../../shared/components/banner/banner';

export const promoBannerEnhancedTagName = 'gl-promo-banner-enhanced';

@customElement(promoBannerEnhancedTagName)
export class GlPromoBannerEnhanced extends LitElement {
	static override shadowRootOptions: ShadowRootInit = {
		...LitElement.shadowRootOptions,
		delegatesFocus: true,
	};

	static override styles = [
		css`
			:host {
				display: block;
				margin-bottom: 1.2rem;
			}

			gl-banner {
				/* Custom styling for promo banner */
				--gl-banner-primary-emphasis-background: var(--vscode-button-background);
				--gl-banner-secondary-emphasis-background: var(--vscode-button-secondaryBackground);
			}

			/* Hide when no promo is available */
			:host([hidden]) {
				display: none;
			}
		`,
	];

	@consume({ context: promosContext })
	private promos!: PromosContext;

	@consume<State>({ context: stateContext, subscribe: true })
	@state()
	private _state!: State;

	@consume<HostIpc>({ context: ipcContext, subscribe: true })
	@state()
	private _ipc!: HostIpc;

	@property({ reflect: true })
	display: BannerDisplay = 'gradient';

	@property()
	sectionKey = 'promoBanner';

	@property()
	title?: string;

	@property()
	body?: string;

	@property()
	primaryButtonText = 'Upgrade Now';

	@property()
	secondaryButtonText = 'Dismiss';

	@state()
	private closed = false;

	@state()
	private currentPromo?: Promo;

	private get isDismissed(): boolean {
		// Check if the section is collapsed in the state
		// The state should include information about collapsed sections
		return false; // For now, we'll rely on the backend state management
	}

	override render(): unknown {
		if (this.closed || this.isDismissed) {
			return nothing;
		}

		return html`${until(
			this.promos.getApplicablePromo(undefined, 'home').then(promo => this.renderBanner(promo)),
			nothing,
		)}`;
	}

	private renderBanner(promo: Promo | undefined) {
		if (!promo?.content?.webview) {
			this.setAttribute('hidden', '');
			return nothing;
		}

		this.removeAttribute('hidden');
		this.currentPromo = promo;

		const content = promo.content.webview;
		const title = this.title || this.extractTextFromHtml(content.link?.html || content.info?.html || '');
		const body = this.body || content.link?.title || 'Special offer available - don\'t miss out!';

		return html`
			<gl-banner
				.display=${this.display}
				.title=${title}
				.body=${body}
				.primaryButton=${this.primaryButtonText}
				.secondaryButton=${this.secondaryButtonText}
				@gl-banner-primary-click=${this.onPrimaryClick}
				@gl-banner-secondary-click=${this.onSecondaryClick}
			></gl-banner>
		`;
	}

	private extractTextFromHtml(html: string): string {
		// Simple HTML tag removal for extracting text content
		return html.replace(/<[^>]*>/g, '').trim();
	}

	private onPrimaryClick() {
		if (this.currentPromo?.content?.webview?.link?.command) {
			// Execute the promo command
			const command = this.currentPromo.content.webview.link.command;
			this._ipc.sendCommand('gitlens.plus.upgrade' as any, {
				source: { source: 'promo-banner' },
			});
		} else {
			// Default upgrade action
			this._ipc.sendCommand('gitlens.plus.upgrade' as any, {
				source: { source: 'promo-banner' },
			});
		}
	}

	private onSecondaryClick() {
		this.dismiss();
	}

	private dismiss() {
		this.closed = true;
		this._ipc.sendCommand(CollapseSectionCommand, {
			section: this.sectionKey,
			collapsed: true,
		});
	}

	// Public method to allow external dismissal
	public dismissBanner() {
		this.dismiss();
	}
}

declare global {
	interface HTMLElementTagNameMap {
		[promoBannerEnhancedTagName]: GlPromoBannerEnhanced;
	}
}
