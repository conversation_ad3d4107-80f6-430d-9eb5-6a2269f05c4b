<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner Component Demo</title>
    <style>
        body {
            font-family: var(--vscode-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
            background-color: var(--vscode-editor-background, #1e1e1e);
            color: var(--vscode-foreground, #cccccc);
            padding: 2rem;
            margin: 0;
        }
        
        .demo-section {
            margin-bottom: 3rem;
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--vscode-panel-border, #3c3c3c);
            padding-bottom: 0.5rem;
        }
        
        .demo-description {
            margin-bottom: 1rem;
            color: var(--vscode-descriptionForeground, #999999);
        }
        
        /* VSCode theme variables simulation */
        :root {
            --vscode-sideBar-background: #252526;
            --vscode-editor-background: #1e1e1e;
            --vscode-button-background: #0e639c;
            --vscode-button-secondaryBackground: #3c3c3c;
            --vscode-foreground: #cccccc;
            --vscode-descriptionForeground: #999999;
            --vscode-panel-border: #3c3c3c;
        }
    </style>
</head>
<body>
    <h1>GitLens Banner Component Demo</h1>
    
    <div class="demo-section">
        <div class="demo-title">Solid Display Mode</div>
        <div class="demo-description">Default solid background matching card component style</div>
        <gl-banner 
            display="solid"
            title="Welcome to GitLens Pro"
            body="Unlock advanced features and boost your productivity with GitLens Pro. Get insights into your code like never before."
            primary-button="Upgrade Now"
            secondary-button="Learn More">
        </gl-banner>
    </div>
    
    <div class="demo-section">
        <div class="demo-title">Outline Display Mode</div>
        <div class="demo-description">Emphasis color outline with secondary background</div>
        <gl-banner 
            display="outline"
            title="New Feature Available"
            body="Check out our latest AI-powered code suggestions that help you write better code faster."
            primary-button="Try It Now"
            secondary-button="Dismiss">
        </gl-banner>
    </div>
    
    <div class="demo-section">
        <div class="demo-title">Gradient Display Mode</div>
        <div class="demo-description">Horizontal gradient from primary to secondary emphasis colors</div>
        <gl-banner 
            display="gradient"
            title="Limited Time Offer"
            body="Save 50% on GitLens Pro subscription. This exclusive offer expires soon!"
            primary-button="Get Discount"
            secondary-button="Maybe Later">
        </gl-banner>
    </div>
    
    <div class="demo-section">
        <div class="demo-title">Gradient Transparent Display Mode</div>
        <div class="demo-description">Same gradient but with 50% transparency</div>
        <gl-banner 
            display="gradient-transparent"
            title="Integration Available"
            body="Connect your GitHub, GitLab, or Bitbucket account to enhance your GitLens experience."
            primary-button="Connect Now"
            secondary-button="Skip">
        </gl-banner>
    </div>
    
    <div class="demo-section">
        <div class="demo-title">Minimal Banner</div>
        <div class="demo-description">Banner with only title and primary button</div>
        <gl-banner 
            display="solid"
            title="Quick Action Required"
            primary-button="Take Action">
        </gl-banner>
    </div>
    
    <div class="demo-section">
        <div class="demo-title">Body Only Banner</div>
        <div class="demo-description">Banner with body text and secondary button only</div>
        <gl-banner 
            display="outline"
            body="This is a simple notification message that doesn't require immediate action."
            secondary-button="Got It">
        </gl-banner>
    </div>

    <script type="module">
        // Import the banner component
        import './banner.js';
        
        // Add event listeners to demonstrate functionality
        document.addEventListener('gl-banner-primary-click', (e) => {
            console.log('Primary button clicked:', e.detail);
            alert('Primary action triggered!');
        });
        
        document.addEventListener('gl-banner-secondary-click', (e) => {
            console.log('Secondary button clicked:', e.detail);
            alert('Secondary action triggered!');
        });
    </script>
</body>
</html>
